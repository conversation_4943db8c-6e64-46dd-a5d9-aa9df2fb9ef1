{"name": "har-porto", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.2.14", "next": "14.2.4", "react": "^18", "react-countup": "^6.5.3", "react-dom": "^18", "react-icons": "^5.2.1", "react-select": "^5.8.3", "swiper": "^11.1.11", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "tailwindcss": "^3.4.1"}}