"use client";

import { useState } from "react"; // Import useState
import { She<PERSON>, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { CiMenuFries } from "react-icons/ci";

const links = [
  {
    name: "home",
    path: "/",
  },
  {
    name: "resume",
    path: "/resume",
  },
  {
    name: "Project",
    path: "/work",
  },
  {
    name: "contact",
    path: "/contact",
  },
];

const MobileNav = () => {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false); // State untuk mengontrol Sheet

  const handleLinkClick = () => {
    setIsOpen(false); // Menutup Sheet saat link diklik
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      {/* Mengontrol open dari Sheet */}
      <SheetTrigger className="flex justify-center items-center" onClick={() => setIsOpen(true)}>
        <CiMenuFries className="text-[32px] text-accent" />
      </SheetTrigger>
      <SheetContent className="flex flex-col overflow-y-auto">
        {/* logo */}
        <div className="mt-32 mb-40 text-center text-2xl">
          <Link href="/" onClick={handleLinkClick}>
            {/* Menutup Sheet saat logo diklik */}
            <h1 className="text-4xl font-primary font-bold tracking-tight">
              <span className="text-accent"> &lt;</span>
              hari&nbsp;
              <span className="text-accent">/&gt;</span>
            </h1>
          </Link>
        </div>
        {/* nav */}
        <nav className="flex flex-col justify-center items-center gap-8">
          {links.map((link, index) => {
            return (
              <Link
                href={link.path}
                key={index}
                onClick={handleLinkClick} // Menutup Sheet saat link diklik
                className={`${link.path === pathname && "text-accent border-b-2 border-accent"} text-xl capitalize hover:text-accent transition-all`}
              >
                {link.name}
              </Link>
            );
          })}
        </nav>
      </SheetContent>
    </Sheet>
  );
};

export default MobileNav;
